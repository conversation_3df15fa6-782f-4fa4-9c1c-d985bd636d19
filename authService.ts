import { httpService } from '@render/services/httpService'
import { apiEndpoints } from '@render/config/httpConfig'

/**
 * 登录请求参数
 */
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
}

/**
 * 登录响应数据
 */
export interface LoginResponse {
  accessToken: string
  refreshToken: string
  expiresIn: number
  user: UserInfo
}

/**
 * 用户信息
 */
export interface UserInfo {
  id: string
  username: string
  email: string
  nickname: string
  avatar?: string
  roles: string[]
  permissions: string[]
}

/**
 * 刷新token请求参数
 */
export interface RefreshTokenRequest {
  refreshToken: string
}

/**
 * 认证服务
 */
export class AuthService {
  /**
   * 用户登录
   */
  static async login(params: LoginRequest): Promise<LoginResponse> {
    const response = await httpService.post<LoginResponse>(apiEndpoints.auth.login, params)

    // 保存token到本地存储
    if (response.Data.accessToken) {
      localStorage.setItem('access_token', response.Data.accessToken)
      localStorage.setItem('refresh_token', response.Data.refreshToken)

      // 设置HTTP服务的认证token
      httpService.setAuthToken(response.Data.accessToken)
    }

    return response.Data
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      await httpService.post(apiEndpoints.auth.logout)
    } finally {
      // 清除本地存储的token
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      
      // 清除HTTP服务的认证token
      httpService.clearAuthToken()
    }
  }

  /**
   * 刷新token
   */
  static async refreshToken(): Promise<LoginResponse> {
    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await httpService.post<LoginResponse>(
      apiEndpoints.auth.refresh,
      { refreshToken }
    )

    // 更新token
    if (response.Data.accessToken) {
      localStorage.setItem('access_token', response.Data.accessToken)
      localStorage.setItem('refresh_token', response.Data.refreshToken)

      // 更新HTTP服务的认证token
      httpService.setAuthToken(response.Data.accessToken)
    }

    return response.Data
  }

  /**
   * 获取用户信息
   */
  static async getProfile(): Promise<UserInfo> {
    const response = await httpService.get<UserInfo>(apiEndpoints.auth.profile)
    return response.Data
  }

  /**
   * 检查是否已登录
   */
  static isLoggedIn(): boolean {
    const token = localStorage.getItem('access_token')
    return !!token
  }

  /**
   * 获取当前token
   */
  static getToken(): string | null {
    return localStorage.getItem('access_token')
  }

  /**
   * 初始化认证状态
   */
  static initAuth(): void {
    const token = this.getToken()
    if (token) {
      httpService.setAuthToken(token)
    }
  }
}

// 自动初始化认证状态
AuthService.initAuth()
