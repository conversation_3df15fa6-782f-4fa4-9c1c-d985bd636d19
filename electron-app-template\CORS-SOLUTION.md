# CORS 跨域问题解决方案

## 问题描述

在开发模式下，前端应用运行在 `http://[::1]:5173`，而U9 API服务器在 `http://*************/U9C/webapi`，由于浏览器的同源策略，会出现CORS跨域错误：

```
来自源"http://[::1]:5173"对"http://*************/U9C/webapi/CommonEntity/Query"的XMLHttpRequest访问已被CORS策略阻止：对预检请求的响应未通过访问控制检查：所请求资源上不存在"Access-Control-Allow-Origin"头。
```

## 解决方案

### 1. Vite 开发服务器代理配置

在 `vite.config.ts` 中添加了代理配置：

```typescript
server: {
    proxy: {
        // 代理U9 API请求
        '/api/u9': {
            target: 'http://*************/U9C/webapi',
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api\/u9/, ''),
            configure: (proxy, options) => {
                // 添加调试日志
                proxy.on('error', (err, req, res) => {
                    console.log('proxy error', err);
                });
                proxy.on('proxyReq', (proxyReq, req, res) => {
                    console.log('Sending Request to the Target:', req.method, req.url);
                });
                proxy.on('proxyRes', (proxyRes, req, res) => {
                    console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
                });
            }
        }
    }
}
```

### 2. HTTP配置更新

更新了 `src/render/config/httpConfig.ts` 中的环境配置：

```typescript
export const envConfig = {
  development: {
    baseURL: '/api/u9', // 使用代理路径
    timeout: 30000,
    enableRetry: true
  },
  
  production: {
    baseURL: 'http://*************/U9C/webapi', // 生产环境直接访问
    timeout: 15000,
    enableRetry: true
  },
  
  test: {
    baseURL: '/api/u9', // 测试环境也使用代理
    timeout: 10000,
    enableRetry: false
  }
}
```

## 工作原理

1. **开发模式**：
   - 前端请求 `/api/u9/CommonEntity/Query`
   - Vite代理服务器接收请求
   - 代理服务器将请求转发到 `http://*************/U9C/webapi/CommonEntity/Query`
   - 代理服务器返回响应给前端

2. **生产模式**：
   - 直接请求 `http://*************/U9C/webapi/CommonEntity/Query`
   - 需要确保生产环境的服务器配置了正确的CORS头

## 使用方法

1. **重启开发服务器**：
   ```bash
   npm run dev
   ```

2. **验证代理工作**：
   - 打开浏览器开发者工具
   - 在Network标签页中查看请求
   - 请求URL应该显示为 `/api/u9/CommonEntity/Query`
   - 在控制台中可以看到代理日志

## 注意事项

1. **开发环境**：代理配置只在开发模式下生效
2. **生产环境**：需要确保生产服务器正确配置CORS头
3. **调试**：代理配置包含了详细的日志，便于调试问题

## 其他解决方案

如果代理方案不适用，还可以考虑：

1. **服务器端配置CORS**：在U9服务器上配置允许跨域的响应头
2. **浏览器扩展**：使用CORS扩展（仅限开发测试）
3. **中间件服务**：创建一个中间件服务来转发请求

## 测试

配置完成后，可以通过以下方式测试：

1. 在搜索框中输入内容
2. 观察浏览器Network标签页中的请求
3. 检查控制台是否有CORS错误
4. 验证是否能正常获取到U9系统的数据
