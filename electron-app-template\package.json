{"name": "autoToExcelForU9", "version": "2.0.0", "description": "A template used by the electron framework", "main": "dist/main/index.js", "author": "zcl", "scripts": {"dev": "chcp 65001 && rimraf dist && vite", "build": "rimraf dist && vite build", "fix": "electron-fix start", "test": "npm run test:main && npm run test:render", "test:render": "vitest run -r src/render --passWithNoTests", "test:main": "vitest run -r src/main --passWithNoTests", "test:e2e": "vitest run", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder uninstall-app-deps", "rebuild": "electron-rebuild"}, "dependencies": {"@types/uuid": "^9.0.8", "axios": "^1.11.0", "electron-log": "5.1.7", "electron-updater": "6.1.8", "lodash-es": "^4.17.21", "mysql2": "^3.9.2", "pinia": "^2.1.7", "reflect-metadata": "0.2.1", "sqlite3": "^5.1.7", "typeorm": "0.3.20", "uuid": "^9.0.1", "vue": "^3.4.19"}, "devDependencies": {"@electron/rebuild": "3.6.0", "@types/lodash-es": "^4.17.6", "@vicons/fluent": "^0.12.0", "@vitejs/plugin-vue": "5.0.4", "@vue/test-utils": "2.0.2", "autoprefixer": "^10.4.17", "electron": "23.3.13", "electron-builder": "24.12.0", "electron-debug": "3.2.0", "electron-devtools-installer": "3.2.0", "electron-fix": "1.1.4", "less": "4.2.0", "less-loader": "12.2.0", "lint-staged": "13.0.3", "naive-ui": "2.38.1", "playwright": "1.26.0", "postcss": "^8.4.35", "rimraf": "3.0.2", "simple-git-hooks": "2.8.0", "tailwindcss": "^3.4.1", "tslib": "2.4.0", "typescript": "4.8.3", "unplugin-vue-define-options": "1.2.3", "vite": "5.1.4", "vite-plugin-doubleshot": "0.0.13", "vitest": "1.3.1", "vue-router": "4.2.5", "vue-tsc": "1.8.27"}, "engines": {"node": "18.12.1"}}