# HTTP API 封装使用指南

本项目提供了一套完整的基于 axios 的 HTTP 请求封装，支持 TypeScript，包含拦截器、错误处理、重试机制、文件上传下载等功能。

## 目录结构

```
src/render/
├── api/
│   ├── index.ts                 # API统一入口
│   ├── services/
│   │   ├── authService.ts       # 认证服务
│   │   └── u9Service.ts         # U9系统服务
│   └── README.md               # 使用文档
├── config/
│   └── httpConfig.ts           # HTTP配置
├── services/
│   └── httpService.ts          # HTTP服务实例
├── types/
│   └── http.ts                 # HTTP类型定义
├── utils/
│   └── http/
│       ├── httpClient.ts       # HTTP客户端
│       └── httpUtils.ts        # HTTP工具类
└── examples/
    └── apiUsage.ts             # 使用示例
```

## 快速开始

### 1. 基本使用

```typescript
import { httpService } from '@render/api'

// GET 请求
const users = await httpService.get('/users')

// POST 请求
const newUser = await httpService.post('/users', {
  name: 'John',
  email: '<EMAIL>'
})

// PUT 请求
const updatedUser = await httpService.put('/users/1', {
  name: 'John Doe'
})

// DELETE 请求
await httpService.delete('/users/1')
```

### 2. 使用服务类

```typescript
import { AuthService, U9Service } from '@render/api'

// 用户登录
const loginResult = await AuthService.login({
  username: 'admin',
  password: '123456'
})

// 获取销售订单
const orders = await U9Service.getSalesOrders({
  page: 1,
  pageSize: 10,
  status: 'pending'
})

// 生成条码
const barcode = await U9Service.generateBarcode({
  orderNumber: 'SO202401001',
  type: 'qr'
})
```

## 主要功能

### 1. 请求拦截器

自动添加认证token、请求ID、时间戳等：

```typescript
// 配置在 httpConfig.ts 中
requestInterceptors: [
  {
    onFulfilled: (config) => {
      // 添加认证token
      const token = localStorage.getItem('access_token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    }
  }
]
```

### 2. 响应拦截器

处理响应数据和错误：

```typescript
responseInterceptors: [
  {
    onFulfilled: (response) => response,
    onRejected: (error) => {
      // 处理401未授权错误
      if (error.response?.status === 401) {
        // 清除token并跳转登录页
        localStorage.removeItem('access_token')
        window.location.href = '/login'
      }
      return Promise.reject(error)
    }
  }
]
```

### 3. 错误处理

```typescript
// 自定义错误处理
const result = await httpService.get('/api/data', {}, {
  customErrorHandler: (error) => {
    console.log('自定义错误处理:', error)
  },
  showError: false // 不显示默认错误消息
})
```

### 4. 请求重试

```typescript
// 配置重试
const result = await httpService.get('/api/unstable', {}, {
  retryCount: 3,
  retryDelay: 1000
})
```

### 5. 请求取消

```typescript
// 发起请求
const promise = httpService.get('/api/slow-endpoint')

// 取消请求
httpService.cancel('/api/slow-endpoint', '用户取消了请求')

// 取消所有请求
httpService.cancelAll()
```

### 6. 文件上传

```typescript
// 单文件上传
const result = await httpService.upload('/files/upload', file, {
  fileField: 'file',
  formData: {
    category: 'document'
  },
  onUploadProgress: (progress) => {
    console.log(`上传进度: ${progress.loaded}/${progress.total}`)
  }
})

// 多文件上传
const result = await httpService.uploadMultiple('/files/upload', files)
```

### 7. 文件下载

```typescript
// 下载文件
const blob = await httpService.download('/files/1/download', 'filename.pdf', {
  onDownloadProgress: (progress) => {
    console.log(`下载进度: ${progress.loaded}/${progress.total}`)
  }
})
```

### 8. 分页查询

```typescript
// 分页查询
const result = await httpService.paginate('/users', {
  page: 1,
  pageSize: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

console.log(result.list)      // 数据列表
console.log(result.total)     // 总数
console.log(result.hasNext)   // 是否有下一页
```

## 配置说明

### 1. 环境配置

在 `httpConfig.ts` 中配置不同环境的参数：

```typescript
export const envConfig = {
  development: {
    baseURL: 'http://localhost:3000/api',
    timeout: 30000
  },
  production: {
    baseURL: 'https://api.example.com',
    timeout: 15000
  }
}
```

### 2. API端点配置

```typescript
export const apiEndpoints = {
  auth: {
    login: '/auth/login',
    logout: '/auth/logout'
  },
  u9: {
    salesOrder: '/u9/sales-orders',
    barcode: '/u9/barcode'
  }
}
```

## 类型定义

项目提供了完整的 TypeScript 类型定义：

```typescript
// 请求配置
interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
  customErrorHandler?: (error: any) => void
  retryCount?: number
  retryDelay?: number
}

// 响应数据
interface ResponseData<T = any> {
  ResCode: number
  Success: boolean
  ResMsg: string
  Data: T
}

// 分页响应
interface PaginationResponse<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
  hasNext: boolean
  hasPrev: boolean
}
```

## 最佳实践

1. **使用服务类**：为不同的业务模块创建专门的服务类
2. **错误处理**：合理使用自定义错误处理器
3. **类型安全**：充分利用 TypeScript 类型定义
4. **请求取消**：对于长时间请求，提供取消功能
5. **进度显示**：文件上传下载时显示进度
6. **缓存策略**：合理使用请求缓存
7. **重试机制**：对不稳定的接口启用重试

## 示例代码

详细的使用示例请参考 `examples/apiUsage.ts` 文件。
