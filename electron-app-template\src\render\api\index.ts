/**
 * API统一入口文件
 */

// 导出HTTP服务
export { httpService } from '@render/services/httpService'

// 导出API服务
export { AuthService } from './services/authService'
export { U9Service } from './services/u9Service'

// 导出类型定义
export type {
  // HTTP相关类型
  RequestConfig,
  ResponseData,
  PaginationParams,
  PaginationResponse,
  UploadConfig,
  DownloadConfig,
  HttpMethod,
  HttpStatusCode
} from '@render/types/http'

export type {
  // 认证相关类型
  LoginRequest,
  LoginResponse,
  UserInfo,
  RefreshTokenRequest
} from './services/authService'

export type {
  // U9相关类型
  SalesOrder,
  SalesOrderItem,
  BarcodeInfo,
  InventoryInfo,
  SalesOrderQuery,
  BarcodeGenerateRequest
} from './services/u9Service'

// 导出配置
export { apiEndpoints, httpConfig } from '@render/config/httpConfig'

// 导出工具类
export { HttpUtils } from '@render/utils/http/httpUtils'
