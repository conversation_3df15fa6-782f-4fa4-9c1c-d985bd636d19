import { httpService } from '@render/services/httpService'
import { apiEndpoints } from '@render/config/httpConfig'
import { PaginationParams, PaginationResponse } from '@render/types/http'

/**
 * 销售订单信息
 */
export interface SalesOrder {
  id:string | null,
  DocNo:String|null
}


/**
 * U9系统服务
 */
export class U9Service {


  /**
   * （模糊查询）获取销售订单号
   */
  static async getSalesOrderDetail(orderNumber: string): Promise<SalesOrder> {
    const response = await httpService.post<SalesOrder>(`${apiEndpoints.u9.CommonEntity}`,{
    PageSize: 10,
    PageIndex: 1,
    Orders: "DocNo desc",
    Filters: [
        {
            Field: orderNumber,
            Operator: "like",
            Value: "DSO",
            Logic: "and"
        }
    ],
    EntityFullName: "UFIDA.U9.SM.SO.SO",
    ReturnFields: [
        "ID",
        "DocNo"
    ]
},{
  toknen:'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJFbnRlcnByaXNlSUQiOiIxMDIiLCJFbnRDb2RlIjoiMTAyIiwiQ3VsdHVyZU5hbWUiOiJ6aC1DTiIsIk9yZ0NvZGUiOiIxMDEiLCJPcmdJRCI6IjEwMDIzMDYwMTAxMTAwMjciLCJPcmdOYW1lIjoiMTAwMjMwNjAxMDExMDI3MCIsIlVzZXJJRCI6IjEwMDIzMDYwMTAxMTAyNzAiLCJVc2VyQ29kZSI6ImFkbWluIiwiTG9naW5EYXRlIjpudWxsLCJVc2VyTmFtZSI6ImFkbWluIiwiRXhwaXJlIjoiMjAyNS0wNy0yNlQyMDoxNDo1OC4yMDUzODMzKzA4OjAwIiwiVmFsaWRBdWRpZW5jZSI6IjEwMjEwMWFkbWluMTc1MzU0NjQ5ODIwNSIsIkVmZmVjdGl2ZU1pbiI6MjQwLCJBcHBJRCI6Im9kb28iLCJUb2tlbkV4dGVuZEtleSI6ImFkbWluQDEwMUAxMDJAb2RvbyJ9.cMwq21EAc5-Z0HoK3AdBfoLnqjg6FkVR7SwThXa2dbc'
})
    return response
  }

  
}
