import { httpService } from '@render/services/httpService'
import { apiEndpoints } from '@render/config/httpConfig'
import { PaginationParams, PaginationResponse } from '@render/types/http'

/**
 * 销售订单信息
 */
export interface SalesOrder {
  id:string | null,
  DocNo:String|null
}


/**
 * U9系统服务
 */
export class U9Service {


  /**
   * （模糊查询）获取销售订单号
   */
  static async getSalesOrder(orderNumber: string): Promise<SalesOrder> {
    const response = await httpService.post<SalesOrder>(`${apiEndpoints.u9.CommonEntity}`,{
    PageSize: 10,
    PageIndex: 1,
    Orders: "DocNo desc",
    Filters: [
        {
            Field: orderNumber,
            Operator: "like",
            Value: "DSO",
            Logic: "and"
        }
    ],
    EntityFullName: "UFIDA.U9.SM.SO.SO",
    ReturnFields: [
        "ID",
        "DocNo"
    ]
})
    return response
  }

  
}
