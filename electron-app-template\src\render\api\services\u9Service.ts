import { httpService } from '@render/services/httpService'
import { apiEndpoints } from '@render/config/httpConfig'
import { PaginationParams, PaginationResponse } from '@render/types/http'

/**
 * 销售订单信息
 */
export interface SalesOrder {
  id: string
  orderNumber: string
  customerName: string
  orderDate: string
  totalAmount: number
  status: 'pending' | 'confirmed' | 'shipped' | 'completed' | 'cancelled'
  items: SalesOrderItem[]
}

/**
 * 销售订单明细
 */
export interface SalesOrderItem {
  id: string
  productCode: string
  productName: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

/**
 * 条码信息
 */
export interface BarcodeInfo {
  id: string
  code: string
  type: 'qr' | 'barcode'
  data: string
  createdAt: string
}

/**
 * 库存信息
 */
export interface InventoryInfo {
  id: string
  productCode: string
  productName: string
  currentStock: number
  availableStock: number
  reservedStock: number
  unit: string
  warehouse: string
  lastUpdated: string
}

/**
 * 销售订单查询参数
 */
export interface SalesOrderQuery extends PaginationParams {
  orderNumber?: string
  customerName?: string
  status?: string
  startDate?: string
  endDate?: string
}

/**
 * 条码生成参数
 */
export interface BarcodeGenerateRequest {
  orderNumber: string
  type?: 'qr' | 'barcode'
  format?: 'png' | 'svg' | 'pdf'
  size?: {
    width: number
    height: number
  }
}

/**
 * U9系统服务
 */
export class U9Service {
  /**
   * 获取销售订单列表
   */
  static async getSalesOrders(params: SalesOrderQuery): Promise<PaginationResponse<SalesOrder>> {
    const response = await httpService.paginate<SalesOrder>(apiEndpoints.u9.salesOrder, params)
    return response.Data
  }

  /**
   * 获取销售订单详情
   */
  static async getSalesOrderDetail(orderNumber: string): Promise<SalesOrder> {
    const response = await httpService.get<SalesOrder>(`${apiEndpoints.u9.salesOrder}/${orderNumber}`)
    return response.Data
  }

  /**
   * 生成条码
   */
  static async generateBarcode(params: BarcodeGenerateRequest): Promise<BarcodeInfo> {
    const response = await httpService.post<BarcodeInfo>(apiEndpoints.u9.barcode, params)
    return response.Data
  }

  /**
   * 批量生成条码
   */
  static async generateBarcodes(orderNumbers: string[]): Promise<BarcodeInfo[]> {
    const response = await httpService.post<BarcodeInfo[]>(`${apiEndpoints.u9.barcode}/batch`, {
      orderNumbers
    })
    return response.Data
  }

  /**
   * 下载条码文件
   */
  static async downloadBarcode(barcodeId: string, filename?: string): Promise<Blob> {
    return httpService.download(`${apiEndpoints.u9.barcode}/${barcodeId}/download`, filename)
  }

  /**
   * 获取库存信息
   */
  static async getInventory(params: {
    productCode?: string
    warehouse?: string
    page?: number
    pageSize?: number
  }): Promise<PaginationResponse<InventoryInfo>> {
    const response = await httpService.paginate<InventoryInfo>(apiEndpoints.u9.inventory, params)
    return response.Data
  }

  /**
   * 获取产品库存详情
   */
  static async getProductInventory(productCode: string): Promise<InventoryInfo[]> {
    const response = await httpService.get<InventoryInfo[]>(`${apiEndpoints.u9.inventory}/product/${productCode}`)
    return response.Data
  }

  /**
   * 更新库存
   */
  static async updateInventory(productCode: string, data: {
    warehouse: string
    quantity: number
    type: 'in' | 'out'
    reason?: string
  }): Promise<InventoryInfo> {
    const response = await httpService.post<InventoryInfo>(`${apiEndpoints.u9.inventory}/update`, {
      productCode,
      ...data
    })
    return response.Data
  }

  /**
   * 导出销售订单
   */
  static async exportSalesOrders(params: SalesOrderQuery): Promise<Blob> {
    return httpService.download(`${apiEndpoints.u9.salesOrder}/export`, undefined, {
      params,
      responseType: 'blob'
    })
  }

  /**
   * 导入销售订单
   */
  static async importSalesOrders(file: File): Promise<{
    success: number
    failed: number
    errors: string[]
  }> {
    const response = await httpService.upload(`${apiEndpoints.u9.salesOrder}/import`, file, {
      fileField: 'file'
    })
    return response.Data
  }

  /**
   * 同步U9数据
   */
  static async syncU9Data(type: 'orders' | 'inventory' | 'all'): Promise<{
    status: 'success' | 'failed'
    message: string
    syncedCount: number
  }> {
    const response = await httpService.post(`${apiEndpoints.u9.salesOrder}/sync`, { type })
    return response.Data
  }

  /**
   * 获取同步状态
   */
  static async getSyncStatus(): Promise<{
    isRunning: boolean
    lastSyncTime: string
    nextSyncTime: string
    status: 'idle' | 'running' | 'error'
  }> {
    const response = await httpService.get(`${apiEndpoints.u9.salesOrder}/sync/status`)
    return response.Data
  }
}
