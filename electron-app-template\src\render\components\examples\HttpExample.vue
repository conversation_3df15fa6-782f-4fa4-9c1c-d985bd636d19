<template>
  <div class="http-example">
    <n-card title="HTTP服务示例" class="example-card">
      <n-space vertical :size="16">
        <!-- 基本请求示例 -->
        <n-card title="基本请求" size="small">
          <n-space vertical :size="12">
            <n-space>
              <n-button @click="testGet" :loading="loading.get" type="primary">
                测试GET请求
              </n-button>
              <n-button @click="testPost" :loading="loading.post" type="info">
                测试POST请求
              </n-button>
            </n-space>
            <n-code v-if="results.basic" :code="results.basic" language="json" />
          </n-space>
        </n-card>

        <!-- 认证示例 -->
        <n-card title="认证服务" size="small">
          <n-space vertical :size="12">
            <n-form inline :model="loginForm">
              <n-form-item label="用户名">
                <n-input v-model:value="loginForm.username" placeholder="请输入用户名" />
              </n-form-item>
              <n-form-item label="密码">
                <n-input 
                  v-model:value="loginForm.password" 
                  type="password" 
                  placeholder="请输入密码" 
                />
              </n-form-item>
              <n-form-item>
                <n-button @click="testLogin" :loading="loading.login" type="primary">
                  登录
                </n-button>
              </n-form-item>
            </n-form>
            <n-space>
              <n-button @click="testLogout" :loading="loading.logout">
                登出
              </n-button>
              <n-button @click="checkAuthStatus">
                检查登录状态
              </n-button>
            </n-space>
            <n-code v-if="results.auth" :code="results.auth" language="json" />
          </n-space>
        </n-card>

        <!-- U9服务示例 -->
        <n-card title="U9系统服务" size="small">
          <n-space vertical :size="12">
            <n-space>
              <n-button @click="testGetOrders" :loading="loading.orders" type="primary">
                获取销售订单
              </n-button>
              <n-button @click="testGenerateBarcode" :loading="loading.barcode" type="info">
                生成条码
              </n-button>
              <n-button @click="testGetInventory" :loading="loading.inventory" type="warning">
                获取库存
              </n-button>
            </n-space>
            <n-code v-if="results.u9" :code="results.u9" language="json" />
          </n-space>
        </n-card>

        <!-- 文件操作示例 -->
        <n-card title="文件操作" size="small">
          <n-space vertical :size="12">
            <n-upload
              :custom-request="handleFileUpload"
              :show-file-list="false"
              accept=".txt,.json,.csv"
            >
              <n-button :loading="loading.upload">
                <template #icon>
                  <n-icon><CloudUpload /></n-icon>
                </template>
                选择文件上传
              </n-button>
            </n-upload>
            <n-progress 
              v-if="uploadProgress > 0" 
              type="line" 
              :percentage="uploadProgress" 
              :show-indicator="true"
            />
            <n-code v-if="results.file" :code="results.file" language="json" />
          </n-space>
        </n-card>

        <!-- 错误处理示例 -->
        <n-card title="错误处理" size="small">
          <n-space vertical :size="12">
            <n-space>
              <n-button @click="testError404" :loading="loading.error" type="error">
                测试404错误
              </n-button>
              <n-button @click="testNetworkError" :loading="loading.network" type="error">
                测试网络错误
              </n-button>
              <n-button @click="testTimeout" :loading="loading.timeout" type="error">
                测试超时
              </n-button>
            </n-space>
            <n-alert v-if="errorMessage" type="error" :title="errorMessage" />
          </n-space>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { CloudUpload } from '@vicons/fluent'
import { 
  httpService, 
  AuthService, 
  U9Service,
  type LoginRequest,
  type SalesOrderQuery,
  type BarcodeGenerateRequest
} from '@render/api'

// 响应式数据
const loading = reactive({
  get: false,
  post: false,
  login: false,
  logout: false,
  orders: false,
  barcode: false,
  inventory: false,
  upload: false,
  error: false,
  network: false,
  timeout: false
})

const results = reactive({
  basic: '',
  auth: '',
  u9: '',
  file: ''
})

const loginForm = reactive({
  username: 'admin',
  password: '123456'
})

const uploadProgress = ref(0)
const errorMessage = ref('')

// 基本请求测试
const testGet = async () => {
  loading.get = true
  try {
    const result = await httpService.get('https://jsonplaceholder.typicode.com/posts/1')
    results.basic = JSON.stringify(result, null, 2)
    window.$message?.success('GET请求成功')
  } catch (error) {
    console.error('GET请求失败:', error)
    window.$message?.error('GET请求失败')
  } finally {
    loading.get = false
  }
}

const testPost = async () => {
  loading.post = true
  try {
    const result = await httpService.post('https://jsonplaceholder.typicode.com/posts', {
      title: 'Test Post',
      body: 'This is a test post',
      userId: 1
    })
    results.basic = JSON.stringify(result, null, 2)
    window.$message?.success('POST请求成功')
  } catch (error) {
    console.error('POST请求失败:', error)
    window.$message?.error('POST请求失败')
  } finally {
    loading.post = false
  }
}

// 认证测试
const testLogin = async () => {
  loading.login = true
  try {
    // 这里使用模拟数据，实际项目中应该调用真实的登录接口
    const mockResult = {
      accessToken: 'mock_access_token_' + Date.now(),
      refreshToken: 'mock_refresh_token_' + Date.now(),
      expiresIn: 3600,
      user: {
        id: '1',
        username: loginForm.username,
        email: '<EMAIL>',
        nickname: '管理员',
        roles: ['admin'],
        permissions: ['*']
      }
    }
    
    // 模拟保存token
    localStorage.setItem('access_token', mockResult.accessToken)
    localStorage.setItem('refresh_token', mockResult.refreshToken)
    
    results.auth = JSON.stringify(mockResult, null, 2)
    window.$message?.success('登录成功（模拟）')
  } catch (error) {
    console.error('登录失败:', error)
    window.$message?.error('登录失败')
  } finally {
    loading.login = false
  }
}

const testLogout = async () => {
  loading.logout = true
  try {
    // 清除token
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    
    results.auth = JSON.stringify({ message: '已登出' }, null, 2)
    window.$message?.success('登出成功')
  } catch (error) {
    console.error('登出失败:', error)
    window.$message?.error('登出失败')
  } finally {
    loading.logout = false
  }
}

const checkAuthStatus = () => {
  const isLoggedIn = AuthService.isLoggedIn()
  const token = AuthService.getToken()
  
  results.auth = JSON.stringify({
    isLoggedIn,
    token: token ? token.substring(0, 20) + '...' : null
  }, null, 2)
}

// U9服务测试
const testGetOrders = async () => {
  loading.orders = true
  try {
    // 模拟销售订单数据
    const mockOrders = {
      list: [
        {
          id: '1',
          orderNumber: 'SO202401001',
          customerName: '客户A',
          orderDate: '2024-01-01',
          totalAmount: 10000,
          status: 'pending'
        },
        {
          id: '2',
          orderNumber: 'SO202401002',
          customerName: '客户B',
          orderDate: '2024-01-02',
          totalAmount: 15000,
          status: 'confirmed'
        }
      ],
      total: 2,
      page: 1,
      pageSize: 10,
      hasNext: false,
      hasPrev: false
    }
    
    results.u9 = JSON.stringify(mockOrders, null, 2)
    window.$message?.success('获取销售订单成功（模拟）')
  } catch (error) {
    console.error('获取销售订单失败:', error)
    window.$message?.error('获取销售订单失败')
  } finally {
    loading.orders = false
  }
}

const testGenerateBarcode = async () => {
  loading.barcode = true
  try {
    // 模拟条码生成
    const mockBarcode = {
      id: 'barcode_' + Date.now(),
      code: 'SO202401001_QR',
      type: 'qr',
      data: 'SO202401001',
      createdAt: new Date().toISOString()
    }
    
    results.u9 = JSON.stringify(mockBarcode, null, 2)
    window.$message?.success('生成条码成功（模拟）')
  } catch (error) {
    console.error('生成条码失败:', error)
    window.$message?.error('生成条码失败')
  } finally {
    loading.barcode = false
  }
}

const testGetInventory = async () => {
  loading.inventory = true
  try {
    // 模拟库存数据
    const mockInventory = {
      list: [
        {
          id: '1',
          productCode: 'PROD001',
          productName: '产品A',
          currentStock: 100,
          availableStock: 80,
          reservedStock: 20,
          unit: '个',
          warehouse: 'WH001'
        }
      ],
      total: 1,
      page: 1,
      pageSize: 10
    }
    
    results.u9 = JSON.stringify(mockInventory, null, 2)
    window.$message?.success('获取库存成功（模拟）')
  } catch (error) {
    console.error('获取库存失败:', error)
    window.$message?.error('获取库存失败')
  } finally {
    loading.inventory = false
  }
}

// 文件上传测试
const handleFileUpload = async ({ file }: any) => {
  loading.upload = true
  uploadProgress.value = 0
  
  try {
    // 模拟文件上传
    const mockUpload = () => {
      return new Promise((resolve) => {
        const interval = setInterval(() => {
          uploadProgress.value += 10
          if (uploadProgress.value >= 100) {
            clearInterval(interval)
            resolve({
              id: 'file_' + Date.now(),
              filename: file.name,
              size: file.size,
              type: file.type,
              url: 'https://example.com/files/uploaded_file'
            })
          }
        }, 200)
      })
    }
    
    const result = await mockUpload()
    results.file = JSON.stringify(result, null, 2)
    window.$message?.success('文件上传成功（模拟）')
  } catch (error) {
    console.error('文件上传失败:', error)
    window.$message?.error('文件上传失败')
  } finally {
    loading.upload = false
    setTimeout(() => {
      uploadProgress.value = 0
    }, 2000)
  }
}

// 错误处理测试
const testError404 = async () => {
  loading.error = true
  errorMessage.value = ''
  
  try {
    await httpService.get('https://jsonplaceholder.typicode.com/posts/999999')
  } catch (error: any) {
    errorMessage.value = `404错误: ${error.message || '资源不存在'}`
  } finally {
    loading.error = false
  }
}

const testNetworkError = async () => {
  loading.network = true
  errorMessage.value = ''
  
  try {
    await httpService.get('https://invalid-domain-that-does-not-exist.com/api')
  } catch (error: any) {
    errorMessage.value = `网络错误: ${error.message || '网络连接失败'}`
  } finally {
    loading.network = false
  }
}

const testTimeout = async () => {
  loading.timeout = true
  errorMessage.value = ''
  
  try {
    await httpService.get('https://httpbin.org/delay/10', {}, { timeout: 2000 })
  } catch (error: any) {
    errorMessage.value = `超时错误: ${error.message || '请求超时'}`
  } finally {
    loading.timeout = false
  }
}
</script>

<style scoped>
.http-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-card {
  margin-bottom: 20px;
}

.n-code {
  max-height: 300px;
  overflow-y: auto;
}
</style>
