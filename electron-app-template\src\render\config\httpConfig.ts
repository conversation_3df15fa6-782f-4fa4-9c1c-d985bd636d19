import { HttpClientConfig } from '@render/types/http'

/**
 * HTTP配置
 */
export const httpConfig: HttpClientConfig = {
  // 基础URL
  baseURL: process.env.NODE_ENV === 'development' 
    ? 'http://localhost:3000/api' 
    : 'https://api.example.com',
  
  // 请求超时时间
  timeout: 15000,
  
  // 默认请求头
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  
  // 启用重试
  enableRetry: true,
  
  // 默认重试次数
  defaultRetryCount: 3,
  
  // 默认重试延迟
  defaultRetryDelay: 1000,
  
  // 请求拦截器
  requestInterceptors: [
    {
      onFulfilled: (config) => {
        // 添加固定的token请求头
        config.headers = config.headers
        config.headers.token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJFbnRlcnByaXNlSUQiOiIxMDIiLCJFbnRDb2RlIjoiMTAyIiwiQ3VsdHVyZU5hbWUiOiJ6aC1DTiIsIk9yZ0NvZGUiOiIxMDEiLCJPcmdJRCI6IjEwMDIzMDYwMTAxMTAwMjciLCJPcmdOYW1lIjoiMTAwMjMwNjAxMDExMDI3MCIsIlVzZXJJRCI6IjEwMDIzMDYwMTAxMTAyNzAiLCJVc2VyQ29kZSI6ImFkbWluIiwiTG9naW5EYXRlIjpudWxsLCJVc2VyTmFtZSI6ImFkbWluIiwiRXhwaXJlIjoiMjAyNS0wNy0yNlQyMDoxNDo1OC4yMDUzODMzKzA4OjAwIiwiVmFsaWRBdWRpZW5jZSI6IjEwMjEwMWFkbWluMTc1MzU0NjQ5ODIwNSIsIkVmZmVjdGl2ZU1pbiI6MjQwLCJBcHBJRCI6Im9kb28iLCJUb2tlbkV4dGVuZEtleSI6ImFkbWluQDEwMUAxMDJAb2RvbyJ9.cMwq21EAc5-Z0HoK3AdBfoLnqjg6FkVR7SwThXa2dbc'

        // 如果需要同时支持Authorization头，可以保留以下代码
        // const authToken = localStorage.getItem('access_token')
        // if (authToken) {
        //   config.headers.Authorization = `Bearer ${authToken}`
        // }

        return config
      },
      onRejected: (error) => {
        return Promise.reject(error)
      }
    }
  ],
  
  // 响应拦截器
  responseInterceptors: [
    {
      onFulfilled: (response) => {
        // 处理响应数据
        return response
      },
      onRejected: (error) => {
        // 处理401未授权错误
        if (error.response?.status === 401) {
          // 清除token
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
          
          // 跳转到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
        }
        
        return Promise.reject(error)
      }
    }
  ]
}

/**
 * API端点配置
 */
export const apiEndpoints = {
  // 认证相关
  auth: {

  },
 
  
  // 文件相关
  file: {
    upload: '/files/upload',
    download: (id: string) => `/files/${id}/download`,
    delete: (id: string) => `/files/${id}`
  },
  
  // U9系统相关
  u9: {
    //公共查询
    CommonEntity: '/CommonEntity/Query',
  }
}

/**
 * 环境配置
 */
export const envConfig = {
  development: {
    baseURL: 'http://192.168.7.237/U9C/webapi',
    timeout: 30000,
    enableRetry: true
  },
  
  production: {
    baseURL: 'http://192.168.7.237/U9C/webapi',
    timeout: 15000,
    enableRetry: true
  },
  
  test: {
    baseURL: 'http://192.168.7.237/U9C/webapi',
    timeout: 10000,
    enableRetry: false
  }
}

/**
 * 获取当前环境配置
 */
export function getCurrentEnvConfig() {
  const env = process.env.NODE_ENV as keyof typeof envConfig
  return envConfig[env] || envConfig.development
}

/**
 * 合并配置
 */
export function mergeConfig(customConfig: Partial<HttpClientConfig> = {}): HttpClientConfig {
  const envConf = getCurrentEnvConfig()
  
  return {
    ...httpConfig,
    ...envConf,
    ...customConfig,
    headers: {
      ...httpConfig.headers,
      ...customConfig.headers
    }
  }
}
