import { HttpClientConfig } from '@render/types/http'

/**
 * HTTP配置
 */
export const httpConfig: HttpClientConfig = {
  // 基础URL
  baseURL: process.env.NODE_ENV === 'development' 
    ? 'http://localhost:3000/api' 
    : 'https://api.example.com',
  
  // 请求超时时间
  timeout: 15000,
  
  // 默认请求头
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  
  // 启用重试
  enableRetry: true,
  
  // 默认重试次数
  defaultRetryCount: 3,
  
  // 默认重试延迟
  defaultRetryDelay: 1000,
  
  // 请求拦截器
  requestInterceptors: [
    {
      onFulfilled: (config) => {
        // 添加认证token
        const token = localStorage.getItem('access_token')
        if (token) {
          config.headers = config.headers || {}
          config.headers.Authorization = `Bearer ${token}`
        }
        
        return config
      },
      onRejected: (error) => {
        return Promise.reject(error)
      }
    }
  ],
  
  // 响应拦截器
  responseInterceptors: [
    {
      onFulfilled: (response) => {
        // 处理响应数据
        return response
      },
      onRejected: (error) => {
        // 处理401未授权错误
        if (error.response?.status === 401) {
          // 清除token
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
          
          // 跳转到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
        }
        
        return Promise.reject(error)
      }
    }
  ]
}

/**
 * API端点配置
 */
export const apiEndpoints = {
  // 认证相关
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    profile: '/auth/profile'
  },
  
  // 用户相关
  user: {
    list: '/users',
    detail: (id: string) => `/users/${id}`,
    create: '/users',
    update: (id: string) => `/users/${id}`,
    delete: (id: string) => `/users/${id}`
  },
  
  // 文件相关
  file: {
    upload: '/files/upload',
    download: (id: string) => `/files/${id}/download`,
    delete: (id: string) => `/files/${id}`
  },
  
  // U9系统相关
  u9: {
    salesOrder: '/u9/sales-orders',
    barcode: '/u9/barcode',
    inventory: '/u9/inventory'
  }
}

/**
 * 环境配置
 */
export const envConfig = {
  development: {
    baseURL: 'http://localhost:3000/api',
    timeout: 30000,
    enableRetry: true
  },
  
  production: {
    baseURL: 'https://api.example.com',
    timeout: 15000,
    enableRetry: true
  },
  
  test: {
    baseURL: 'http://test-api.example.com',
    timeout: 10000,
    enableRetry: false
  }
}

/**
 * 获取当前环境配置
 */
export function getCurrentEnvConfig() {
  const env = process.env.NODE_ENV as keyof typeof envConfig
  return envConfig[env] || envConfig.development
}

/**
 * 合并配置
 */
export function mergeConfig(customConfig: Partial<HttpClientConfig> = {}): HttpClientConfig {
  const envConf = getCurrentEnvConfig()
  
  return {
    ...httpConfig,
    ...envConf,
    ...customConfig,
    headers: {
      ...httpConfig.headers,
      ...customConfig.headers
    }
  }
}
