/**
 * API使用示例
 */

import { 
  httpService, 
  AuthService, 
  U9Service,
  type LoginRequest,
  type SalesOrderQuery,
  type BarcodeGenerateRequest
} from '@render/api'

/**
 * 认证相关示例
 */
export class AuthExample {
  /**
   * 登录示例
   */
  static async loginExample() {
    try {
      const loginData: LoginRequest = {
        username: 'admin',
        password: '123456'
      }
      
      const response = await AuthService.login(loginData)
      console.log('登录成功:', response)
      
      // 获取用户信息
      const userInfo = await AuthService.getProfile()
      console.log('用户信息:', userInfo)
      
    } catch (error) {
      console.error('登录失败:', error)
    }
  }

  /**
   * 登出示例
   */
  static async logoutExample() {
    try {
      await AuthService.logout()
      console.log('登出成功')
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  /**
   * 刷新token示例
   */
  static async refreshTokenExample() {
    try {
      const response = await AuthService.refreshToken()
      console.log('Token刷新成功:', response)
    } catch (error) {
      console.error('Token刷新失败:', error)
    }
  }
}

/**
 * U9系统相关示例
 */
export class U9Example {
  /**
   * 获取销售订单列表示例
   */
  static async getSalesOrdersExample() {
    try {
      const query: SalesOrderQuery = {
        page: 1,
        pageSize: 10,
        status: 'pending',
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      }
      
      const response = await U9Service.getSalesOrders(query)
      console.log('销售订单列表:', response)
      
      return response
    } catch (error) {
      console.error('获取销售订单失败:', error)
    }
  }

  /**
   * 生成条码示例
   */
  static async generateBarcodeExample() {
    try {
      const params: BarcodeGenerateRequest = {
        orderNumber: 'SO202401001',
        type: 'qr',
        format: 'png',
        size: {
          width: 200,
          height: 200
        }
      }
      
      const barcode = await U9Service.generateBarcode(params)
      console.log('条码生成成功:', barcode)
      
      // 下载条码文件
      const blob = await U9Service.downloadBarcode(barcode.id, 'barcode.png')
      console.log('条码文件下载成功:', blob)
      
      return barcode
    } catch (error) {
      console.error('生成条码失败:', error)
    }
  }

  /**
   * 批量生成条码示例
   */
  static async batchGenerateBarcodeExample() {
    try {
      const orderNumbers = ['SO202401001', 'SO202401002', 'SO202401003']
      const barcodes = await U9Service.generateBarcodes(orderNumbers)
      console.log('批量生成条码成功:', barcodes)
      
      return barcodes
    } catch (error) {
      console.error('批量生成条码失败:', error)
    }
  }

  /**
   * 获取库存信息示例
   */
  static async getInventoryExample() {
    try {
      const params = {
        productCode: 'PROD001',
        warehouse: 'WH001',
        page: 1,
        pageSize: 20
      }
      
      const inventory = await U9Service.getInventory(params)
      console.log('库存信息:', inventory)
      
      return inventory
    } catch (error) {
      console.error('获取库存信息失败:', error)
    }
  }

  /**
   * 导出销售订单示例
   */
  static async exportSalesOrdersExample() {
    try {
      const query: SalesOrderQuery = {
        page: 1,
        pageSize: 1000,
        status: 'completed'
      }
      
      const blob = await U9Service.exportSalesOrders(query)
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'sales_orders.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      console.log('销售订单导出成功')
    } catch (error) {
      console.error('导出销售订单失败:', error)
    }
  }

  /**
   * 同步U9数据示例
   */
  static async syncU9DataExample() {
    try {
      // 开始同步
      const syncResult = await U9Service.syncU9Data('all')
      console.log('同步结果:', syncResult)
      
      // 检查同步状态
      const status = await U9Service.getSyncStatus()
      console.log('同步状态:', status)
      
      return syncResult
    } catch (error) {
      console.error('同步U9数据失败:', error)
    }
  }
}

/**
 * 文件操作示例
 */
export class FileExample {
  /**
   * 文件上传示例
   */
  static async uploadFileExample(file: File) {
    try {
      const result = await httpService.upload('/files/upload', file, {
        fileField: 'file',
        formData: {
          category: 'document',
          description: '测试文件'
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          console.log(`上传进度: ${progress}%`)
        }
      })
      
      console.log('文件上传成功:', result)
      return result
    } catch (error) {
      console.error('文件上传失败:', error)
    }
  }

  /**
   * 多文件上传示例
   */
  static async uploadMultipleFilesExample(files: File[]) {
    try {
      const result = await httpService.uploadMultiple('/files/upload-multiple', files, {
        fileField: 'files',
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          console.log(`批量上传进度: ${progress}%`)
        }
      })
      
      console.log('多文件上传成功:', result)
      return result
    } catch (error) {
      console.error('多文件上传失败:', error)
    }
  }

  /**
   * 文件下载示例
   */
  static async downloadFileExample(fileId: string) {
    try {
      const blob = await httpService.download(`/files/${fileId}/download`, `file_${fileId}.pdf`, {
        onDownloadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          console.log(`下载进度: ${progress}%`)
        }
      })
      
      console.log('文件下载成功:', blob)
      return blob
    } catch (error) {
      console.error('文件下载失败:', error)
    }
  }
}

/**
 * 错误处理示例
 */
export class ErrorHandlingExample {
  /**
   * 自定义错误处理示例
   */
  static async customErrorHandlingExample() {
    try {
      const result = await httpService.get('/api/test', {}, {
        customErrorHandler: (error) => {
          console.log('自定义错误处理:', error)
          // 这里可以添加自定义的错误处理逻辑
        },
        showError: false // 不显示默认错误消息
      })
      
      return result
    } catch (error) {
      console.error('请求失败:', error)
    }
  }

  /**
   * 请求重试示例
   */
  static async retryExample() {
    try {
      const result = await httpService.get('/api/unstable-endpoint', {}, {
        retryCount: 5,
        retryDelay: 2000
      })
      
      return result
    } catch (error) {
      console.error('重试后仍然失败:', error)
    }
  }

  /**
   * 请求取消示例
   */
  static async cancelRequestExample() {
    try {
      // 发起请求
      const promise = httpService.get('/api/slow-endpoint')
      
      // 3秒后取消请求
      setTimeout(() => {
        httpService.cancel('/api/slow-endpoint', '用户取消了请求')
      }, 3000)
      
      const result = await promise
      return result
    } catch (error) {
      console.error('请求被取消或失败:', error)
    }
  }
}
