import { HttpClient } from '@render/utils/http/httpClient'
import { mergeConfig } from '@render/config/httpConfig'
import { RequestConfig, ResponseData, PaginationParams, PaginationResponse } from '@render/types/http'

/**
 * HTTP服务类
 */
class HttpService {
  private client: HttpClient

  constructor() {
    const config = mergeConfig()
    this.client = new HttpClient(config)
  }

  /**
   * 获取HTTP客户端实例
   */
  getClient(): HttpClient {
    return this.client
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<T> {
    const response = await this.client.get<T>(url, params, config)
    return response.data
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    const response = await this.client.post<T>(url, data, config)
    return response.data
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    const response = await this.client.put<T>(url, data, config)
    return response.data
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    const response = await this.client.delete<T>(url, config)
    return response.data
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    const response = await this.client.patch<T>(url, data, config)
    return response.data
  }

  /**
   * 分页查询
   */
  async paginate<T = any>(
    url: string, 
    params: PaginationParams & Record<string, any> = { page: 1, pageSize: 10 }, 
    config?: RequestConfig
  ): Promise<PaginationResponse<T>> {
    const response = await this.client.paginate<T>(url, params, config)
    return response.data
  }

  /**
   * 文件上传
   */
  async upload<T = any>(url: string, file: File | Blob, config?: any): Promise<T> {
    const response = await this.client.upload<T>(url, file, config)
    return response.data
  }

  /**
   * 多文件上传
   */
  async uploadMultiple<T = any>(url: string, files: (File | Blob)[], config?: any): Promise<T> {
    const response = await this.client.uploadMultiple<T>(url, files, config)
    return response.data
  }

  /**
   * 文件下载
   */
  async download(url: string, filename?: string, config?: any): Promise<Blob> {
    return this.client.download(url, { ...config, filename })
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(url: string, config?: RequestConfig) {
    return this.client.getFileInfo(url, config)
  }

  /**
   * 取消请求
   */
  cancel(url: string, message?: string): void {
    this.client.cancel(url, message)
  }

  /**
   * 取消所有请求
   */
  cancelAll(message?: string): void {
    this.client.cancelAll(message)
  }

  /**
   * 设置默认配置
   */
  setDefaults(config: any): void {
    this.client.setDefaults(config)
  }

  /**
   * 设置认证token
   */
  setAuthToken(token: string): void {
    this.setDefaults({
      headers: {
        Authorization: `Bearer ${token}`
      }
    })
  }

  /**
   * 清除认证token
   */
  clearAuthToken(): void {
    this.setDefaults({
      headers: {
        Authorization: undefined
      }
    })
  }
}

// 创建单例实例
export const httpService = new HttpService()

// 导出类型和工具
export { HttpService }
export * from '@render/types/http'
export * from '@render/utils/http/httpUtils'
