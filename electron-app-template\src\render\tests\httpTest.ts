/**
 * HTTP封装测试文件
 */

import { httpService, AuthService, U9Service } from '@render/api'

/**
 * 测试HTTP基本功能
 */
export class HttpTest {
  /**
   * 测试基本请求
   */
  static async testBasicRequests() {
    console.log('=== 测试基本请求 ===')
    
    try {
      // 测试GET请求
      console.log('测试GET请求...')
      const getResult = await httpService.get('https://jsonplaceholder.typicode.com/posts/1')
      console.log('GET请求成功:', getResult)
      
      // 测试POST请求
      console.log('测试POST请求...')
      const postResult = await httpService.post('https://jsonplaceholder.typicode.com/posts', {
        title: 'Test Post',
        body: 'This is a test post',
        userId: 1
      })
      console.log('POST请求成功:', postResult)
      
    } catch (error) {
      console.error('基本请求测试失败:', error)
    }
  }

  /**
   * 测试错误处理
   */
  static async testErrorHandling() {
    console.log('=== 测试错误处理 ===')
    
    try {
      // 测试404错误
      await httpService.get('https://jsonplaceholder.typicode.com/posts/999999')
    } catch (error) {
      console.log('404错误处理正常:', error)
    }
    
    try {
      // 测试网络错误
      await httpService.get('https://invalid-domain-that-does-not-exist.com/api')
    } catch (error) {
      console.log('网络错误处理正常:', error)
    }
  }

  /**
   * 测试请求取消
   */
  static async testRequestCancellation() {
    console.log('=== 测试请求取消 ===')
    
    try {
      // 发起一个慢请求
      const promise = httpService.get('https://httpbin.org/delay/5')
      
      // 1秒后取消请求
      setTimeout(() => {
        httpService.cancel('https://httpbin.org/delay/5', '测试取消')
      }, 1000)
      
      await promise
    } catch (error) {
      console.log('请求取消测试正常:', error)
    }
  }

  /**
   * 测试重试机制
   */
  static async testRetryMechanism() {
    console.log('=== 测试重试机制 ===')
    
    try {
      // 测试重试（使用一个不稳定的端点）
      const result = await httpService.get('https://httpbin.org/status/500', {}, {
        retryCount: 2,
        retryDelay: 500,
        showError: false
      })
      console.log('重试成功:', result)
    } catch (error) {
      console.log('重试机制测试正常（预期失败）:', error)
    }
  }

  /**
   * 测试分页查询
   */
  static async testPagination() {
    console.log('=== 测试分页查询 ===')
    
    try {
      const result = await httpService.paginate('https://jsonplaceholder.typicode.com/posts', {
        page: 1,
        pageSize: 10,
        _limit: 10,
        _page: 1
      })
      console.log('分页查询成功:', result)
    } catch (error) {
      console.error('分页查询测试失败:', error)
    }
  }

  /**
   * 测试文件操作（模拟）
   */
  static async testFileOperations() {
    console.log('=== 测试文件操作 ===')
    
    try {
      // 创建一个模拟文件
      const mockFile = new File(['Hello World'], 'test.txt', { type: 'text/plain' })
      
      // 测试文件上传（使用httpbin作为测试端点）
      const uploadResult = await httpService.upload('https://httpbin.org/post', mockFile, {
        fileField: 'file',
        formData: {
          description: '测试文件'
        }
      })
      console.log('文件上传测试成功:', uploadResult)
      
    } catch (error) {
      console.error('文件操作测试失败:', error)
    }
  }

  /**
   * 测试认证服务（模拟）
   */
  static async testAuthService() {
    console.log('=== 测试认证服务 ===')
    
    try {
      // 检查登录状态
      const isLoggedIn = AuthService.isLoggedIn()
      console.log('当前登录状态:', isLoggedIn)
      
      // 获取token
      const token = AuthService.getToken()
      console.log('当前token:', token)
      
    } catch (error) {
      console.error('认证服务测试失败:', error)
    }
  }

  /**
   * 运行所有测试
   */
  static async runAllTests() {
    console.log('开始运行HTTP封装测试...')
    
    await this.testBasicRequests()
    await this.testErrorHandling()
    await this.testRequestCancellation()
    await this.testRetryMechanism()
    await this.testPagination()
    await this.testFileOperations()
    await this.testAuthService()
    
    console.log('所有测试完成!')
  }
}

// 如果在浏览器环境中，可以在控制台运行测试
if (typeof window !== 'undefined') {
  // 将测试函数挂载到window对象，方便在控制台调用
  (window as any).HttpTest = HttpTest
  
  console.log('HTTP测试已加载，可以在控制台运行以下命令:')
  console.log('HttpTest.runAllTests() - 运行所有测试')
  console.log('HttpTest.testBasicRequests() - 测试基本请求')
  console.log('HttpTest.testErrorHandling() - 测试错误处理')
}
