import { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'

/**
 * HTTP请求方法枚举
 */
export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
  HEAD = 'HEAD',
  OPTIONS = 'OPTIONS'
}

/**
 * HTTP状态码枚举
 */
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504
}

/**
 * 请求配置接口
 */
export interface RequestConfig extends AxiosRequestConfig {
  /** 是否显示加载状态 */
  showLoading?: boolean
  /** 是否显示错误消息 */
  showError?: boolean
  /** 是否显示成功消息 */
  showSuccess?: boolean
  /** 自定义错误处理 */
  customErrorHandler?: (error: any) => void
  /** 重试次数 */
  retryCount?: number
  /** 重试延迟(ms) */
  retryDelay?: number
}

/**
 * 响应数据接口
 */
export interface ResponseData<T = any> {
  /** 响应状态码 */
  ResCode: number
  /** 请求是否成功 */
  Success: boolean
  /** 响应消息 */
  ResMsg: string
  /** 响应数据 */
  Data: T
}

/**
 * 分页请求参数
 */
export interface PaginationParams {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
}

/**
 * 分页响应数据
 */
export interface PaginationResponse<T = any> {
  /** 数据列表 */
  list: T[]
  /** 总数 */
  total: number
  /** 当前页 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 总页数 */
  totalPages: number
  /** 是否有下一页 */
  hasNext: boolean
  /** 是否有上一页 */
  hasPrev: boolean
}

/**
 * 请求拦截器配置
 */
export interface RequestInterceptorConfig {
  /** 请求前处理 */
  onFulfilled?: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>
  /** 请求错误处理 */
  onRejected?: (error: any) => any
}

/**
 * 响应拦截器配置
 */
export interface ResponseInterceptorConfig {
  /** 响应成功处理 */
  onFulfilled?: (response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>
  /** 响应错误处理 */
  onRejected?: (error: any) => any
}

/**
 * HTTP客户端配置
 */
export interface HttpClientConfig extends AxiosRequestConfig {
  /** 请求拦截器 */
  requestInterceptors?: RequestInterceptorConfig[]
  /** 响应拦截器 */
  responseInterceptors?: ResponseInterceptorConfig[]
  /** 是否启用请求重试 */
  enableRetry?: boolean
  /** 默认重试次数 */
  defaultRetryCount?: number
  /** 默认重试延迟 */
  defaultRetryDelay?: number
}

/**
 * 上传文件配置
 */
export interface UploadConfig extends RequestConfig {
  /** 文件字段名 */
  fileField?: string
  /** 额外的表单数据 */
  formData?: Record<string, any>
  /** 上传进度回调 */
  onUploadProgress?: (progressEvent: any) => void
}

/**
 * 下载文件配置
 */
export interface DownloadConfig extends RequestConfig {
  /** 文件名 */
  filename?: string
  /** 下载进度回调 */
  onDownloadProgress?: (progressEvent: any) => void
}
