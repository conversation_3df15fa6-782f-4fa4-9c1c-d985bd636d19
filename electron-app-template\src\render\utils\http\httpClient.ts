import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, CancelTokenSource } from 'axios'
import { 
  HttpClientConfig, 
  RequestConfig, 
  ResponseData, 
  HttpMethod,
  PaginationParams,
  PaginationResponse,
  UploadConfig,
  DownloadConfig
} from '@render/types/http'
import { HttpUtils } from './httpUtils'

/**
 * HTTP客户端类
 */
export class HttpClient {
  private instance: AxiosInstance
  private cancelTokenSources: Map<string, CancelTokenSource> = new Map()
  private config: HttpClientConfig

  constructor(config: HttpClientConfig = {}) {
    this.config = {
      timeout: 10000,
      enableRetry: true,
      defaultRetryCount: 3,
      defaultRetryDelay: 1000,
      ...config
    }

    this.instance = axios.create(this.config)
    this.setupInterceptors()
  }

  /**
   * 设置拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加请求ID
        config.headers = config.headers 
        config.headers['X-Request-ID'] = HttpUtils.generateRequestId()
        
        // 添加时间戳防止缓存
        if (config.method?.toLowerCase() === 'get') {
          config.params = config.params || {}
          config.params._t = Date.now()
        }

        console.log(`[HTTP Request] ${config.method?.toUpperCase()} ${config.url}`, {
          params: config.params,
          data: config.data,
          headers: config.headers
        })

        return config
      },
      (error) => {
        console.error('[HTTP Request Error]', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        console.log(`[HTTP Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
          status: response.status,
          data: response.data
        })

        return response
      },
      async (error) => {
        console.error('[HTTP Response Error]', error)

        const config = error.config as RequestConfig
        
        // 如果启用重试且满足重试条件
        if (this.shouldRetry(error, config)) {
          return this.retryRequest(config)
        }

        return Promise.reject(error)
      }
    )

    // 添加自定义拦截器
    this.addCustomInterceptors()
  }

  /**
   * 添加自定义拦截器
   */
  private addCustomInterceptors(): void {
    // 请求拦截器
    if (this.config.requestInterceptors) {
      this.config.requestInterceptors.forEach(interceptor => {
        this.instance.interceptors.request.use(
          interceptor.onFulfilled,
          interceptor.onRejected
        )
      })
    }

    // 响应拦截器
    if (this.config.responseInterceptors) {
      this.config.responseInterceptors.forEach(interceptor => {
        this.instance.interceptors.response.use(
          interceptor.onFulfilled,
          interceptor.onRejected
        )
      })
    }
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: any, config: RequestConfig): boolean {
    if (!this.config.enableRetry) return false
    if (config.retryCount === undefined) config.retryCount = 0
    
    const maxRetries = config.retryCount ?? this.config.defaultRetryCount ?? 3
    
    // 已达到最大重试次数
    if (config.retryCount >= maxRetries) return false
    
    // 只对特定错误进行重试
    return HttpUtils.isNetworkError(error) || 
           HttpUtils.isTimeoutError(error) ||
           (error.response && error.response.status >= 500)
  }

  /**
   * 重试请求
   */
  private async retryRequest(config: RequestConfig): Promise<AxiosResponse> {
    config.retryCount = (config.retryCount || 0) + 1
    
    const delay = config.retryDelay ?? this.config.defaultRetryDelay ?? 1000
    await HttpUtils.delay(delay * config.retryCount)
    
    console.log(`[HTTP Retry] ${config.method?.toUpperCase()} ${config.url} (${config.retryCount}/${config.retryCount ?? this.config.defaultRetryCount})`)
    
    return this.instance.request(config)
  }

  /**
   * 通用请求方法
   */
  async request<T = any>(config: RequestConfig): Promise<ResponseData<T>> {
    try {
      // 处理取消令牌
      if (config.cancelToken) {
        const source = axios.CancelToken.source()
        config.cancelToken = source.token
        
        if (config.url) {
          this.cancelTokenSources.set(config.url, source)
        }
      }

      const response = await this.instance.request<ResponseData<T>>(config)
      
      // 清理取消令牌
      if (config.url) {
        this.cancelTokenSources.delete(config.url)
      }

      return response.data
    } catch (error: any) {
      // 处理错误
      if (config.customErrorHandler) {
        config.customErrorHandler(error)
      } else {
        this.handleError(error, config)
      }
      
      throw error
    }
  }

  /**
   * 错误处理
   */
  private handleError(error: any, config: RequestConfig): void {
    const message = HttpUtils.getErrorMessage(error)
    
    if (config.showError !== false) {
      // 这里可以集成消息提示组件
      console.error('[HTTP Error]', message)
      
      // 如果有全局错误处理器，可以在这里调用
      if (window.$message) {
        window.$message.error(message)
      }
    }
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return this.request<T>({
      method: HttpMethod.GET,
      url,
      params,
      ...config
    })
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return this.request<T>({
      method: HttpMethod.POST,
      url,
      data,
      ...config
    })
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return this.request<T>({
      method: HttpMethod.PUT,
      url,
      data,
      ...config
    })
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: RequestConfig): Promise<ResponseData<T>> {
    return this.request<T>({
      method: HttpMethod.DELETE,
      url,
      ...config
    })
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    return this.request<T>({
      method: HttpMethod.PATCH,
      url,
      data,
      ...config
    })
  }

  /**
   * 分页查询
   */
  async paginate<T = any>(
    url: string, 
    params: PaginationParams & Record<string, any> = { page: 1, pageSize: 10 }, 
    config?: RequestConfig
  ): Promise<ResponseData<PaginationResponse<T>>> {
    return this.get<PaginationResponse<T>>(url, params, config)
  }

  /**
   * 取消请求
   */
  cancel(url: string, message?: string): void {
    const source = this.cancelTokenSources.get(url)
    if (source) {
      source.cancel(message || '请求已取消')
      this.cancelTokenSources.delete(url)
    }
  }

  /**
   * 取消所有请求
   */
  cancelAll(message?: string): void {
    this.cancelTokenSources.forEach((source, url) => {
      source.cancel(message || '所有请求已取消')
    })
    this.cancelTokenSources.clear()
  }

  /**
   * 获取axios实例
   */
  getInstance(): AxiosInstance {
    return this.instance
  }

  /**
   * 设置默认配置
   */
  setDefaults(config: Partial<AxiosRequestConfig>): void {
    Object.assign(this.instance.defaults, config)
  }

  /**
   * 文件上传
   */
  async upload<T = any>(
    url: string,
    file: File | Blob,
    config: UploadConfig = {}
  ): Promise<ResponseData<T>> {
    const formData = new FormData()
    const fileField = config.fileField || 'file'

    formData.append(fileField, file)

    // 添加额外的表单数据
    if (config.formData) {
      Object.keys(config.formData).forEach(key => {
        formData.append(key, config.formData![key])
      })
    }

    return this.request<T>({
      method: HttpMethod.POST,
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: config.onUploadProgress,
      ...config
    })
  }

  /**
   * 多文件上传
   */
  async uploadMultiple<T = any>(
    url: string,
    files: (File | Blob)[],
    config: UploadConfig = {}
  ): Promise<ResponseData<T>> {
    const formData = new FormData()
    const fileField = config.fileField || 'files'

    files.forEach((file, index) => {
      formData.append(`${fileField}[${index}]`, file)
    })

    // 添加额外的表单数据
    if (config.formData) {
      Object.keys(config.formData).forEach(key => {
        formData.append(key, config.formData![key])
      })
    }

    return this.request<T>({
      method: HttpMethod.POST,
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: config.onUploadProgress,
      ...config
    })
  }

  /**
   * 文件下载
   */
  async download(
    url: string,
    config: DownloadConfig = {}
  ): Promise<Blob> {
    const response = await this.instance.request({
      method: HttpMethod.GET,
      url,
      responseType: 'blob',
      onDownloadProgress: config.onDownloadProgress,
      ...config
    })

    // 如果指定了文件名，创建下载链接
    if (config.filename) {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = config.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    }

    return response.data
  }

  /**
   * 获取文件信息（不下载文件内容）
   */
  async getFileInfo(url: string, config?: RequestConfig): Promise<{
    size: number
    type: string
    lastModified?: string
  }> {
    const response = await this.instance.request({
      method: HttpMethod.HEAD,
      url,
      ...config
    })

    return {
      size: parseInt(response.headers['content-length'] || '0'),
      type: response.headers['content-type'] || '',
      lastModified: response.headers['last-modified']
    }
  }
}
