import { AxiosError, AxiosResponse } from 'axios'
import { HttpStatusCode, ResponseData } from '@render/types/http'

/**
 * HTTP工具类
 */
export class HttpUtils {
  /**
   * 判断是否为网络错误
   */
  static isNetworkError(error: AxiosError): boolean {
    return !error.response && error.code === 'NETWORK_ERROR'
  }

  /**
   * 判断是否为超时错误
   */
  static isTimeoutError(error: AxiosError): boolean {
    return error.code === 'ECONNABORTED' || error.message.includes('timeout')
  }

  /**
   * 判断是否为取消请求
   */
  static isCancelError(error: AxiosError): boolean {
    return error.code === 'ERR_CANCELED'
  }

  /**
   * 获取错误消息
   */
  static getErrorMessage(error: AxiosError): string {
    if (this.isNetworkError(error)) {
      return '网络连接失败，请检查网络设置'
    }
    
    if (this.isTimeoutError(error)) {
      return '请求超时，请稍后重试'
    }
    
    if (this.isCancelError(error)) {
      return '请求已取消'
    }

    if (error.response) {
      const { status, data } = error.response

      // 尝试从响应数据中获取错误消息
      if (data && typeof data === 'object') {
        const errorData = data as any
        if (errorData.ResMsg) return errorData.ResMsg
        if (errorData.message) return errorData.message
        if (errorData.msg) return errorData.msg
        if (errorData.error) return errorData.error
      }
      
      // 根据状态码返回默认消息
      switch (status) {
        case HttpStatusCode.BAD_REQUEST:
          return '请求参数错误'
        case HttpStatusCode.UNAUTHORIZED:
          return '未授权，请重新登录'
        case HttpStatusCode.FORBIDDEN:
          return '访问被拒绝'
        case HttpStatusCode.NOT_FOUND:
          return '请求的资源不存在'
        case HttpStatusCode.METHOD_NOT_ALLOWED:
          return '请求方法不被允许'
        case HttpStatusCode.CONFLICT:
          return '请求冲突'
        case HttpStatusCode.INTERNAL_SERVER_ERROR:
          return '服务器内部错误'
        case HttpStatusCode.BAD_GATEWAY:
          return '网关错误'
        case HttpStatusCode.SERVICE_UNAVAILABLE:
          return '服务暂时不可用'
        case HttpStatusCode.GATEWAY_TIMEOUT:
          return '网关超时'
        default:
          return `请求失败 (${status})`
      }
    }

    return error.message || '未知错误'
  }

  /**
   * 判断响应是否成功
   */
  static isResponseSuccess(response: AxiosResponse<ResponseData>): boolean {
    const { status } = response
    const { ResCode, Success } = response.data || {}

    // HTTP状态码成功且业务状态码成功
    return status >= 200 && status < 300 && Success === true && (ResCode === 0 || ResCode === 200)
  }

  /**
   * 格式化URL参数
   */
  static formatUrlParams(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()
    
    Object.keys(params).forEach(key => {
      const value = params[key]
      if (value !== null && value !== undefined && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    
    return searchParams.toString()
  }

  /**
   * 构建完整URL
   */
  static buildUrl(baseUrl: string, path: string, params?: Record<string, any>): string {
    let url = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl
    url += path.startsWith('/') ? path : `/${path}`
    
    if (params && Object.keys(params).length > 0) {
      const queryString = this.formatUrlParams(params)
      url += `?${queryString}`
    }
    
    return url
  }

  /**
   * 深度合并对象
   */
  static deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
    const result: Record<string, any> = { ...target }

    Object.keys(source).forEach(key => {
      const sourceValue = source[key]
      const targetValue = result[key]

      if (this.isObject(sourceValue) && this.isObject(targetValue)) {
        result[key] = this.deepMerge(targetValue, sourceValue)
      } else {
        result[key] = sourceValue
      }
    })

    return result as T
  }

  /**
   * 判断是否为对象
   */
  private static isObject(value: any): value is Record<string, any> {
    return value !== null && typeof value === 'object' && !Array.isArray(value)
  }

  /**
   * 延迟执行
   */
  static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 生成请求ID
   */
  static generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 验证URL格式
   */
  static isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }
}
