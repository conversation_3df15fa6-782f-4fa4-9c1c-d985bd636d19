<template>
  <div class="baidu-container">
    <!-- 顶部导航 -->
    <div class="top-nav">
      <div class="nav-right">
        <n-button text size="small" class="nav-link">
          <a href="http://192.168.7.237/u9c/mvc/main/index">U9系统</a>
        </n-button>
        <n-button text size="small" class="nav-link">
          <a href="http://202.104.155.199:8088/zx_cloud/">标签系统</a>
        </n-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- Logo区域 -->
      <div class="logo-section">
        <img src="https://img03.71360.com/w3/w426o/20240402/17d52803bad6c4c72e6f647e58791345.jpg" alt="Logo"
          class="logo-image" @error="handleImageError" />
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="search-wrapper">
          <n-input v-model:value="searchQuery" size="large" placeholder="请输入销售订单号进行搜索" clearable
            @keyup.enter="handleSearch" @input="handleInputChange" :loading="searchLoading"
            class="">
            <template #prefix>
              <n-icon :size="16" class="search-icon">
                <SearchIcon />
              </n-icon>
            </template>
            <template #suffix>
              <n-button text size="small" @click="handleSearch" class="search-btn-suffix">
                <n-icon :size="18">
                  <SearchIcon />
                </n-icon>
              </n-button>
            </template>
          </n-input>
        </div>

        <!-- 搜索按钮 -->
        <div class="button-section">
          <n-space :size="12">
            <n-button type="primary" size="medium" @click="handleSearch" class="search-button primary-btn">
              搜索一下
            </n-button>
          </n-space>
        </div>
      </div>

      <!-- 热搜推荐 -->
      <div class="hot-search">
        <n-space :size="8" :wrap="true">
          <n-tag v-for="(item, index) in hotSearchList" :key="index" :bordered="false" size="small" class="hot-tag"
            @click="searchHotItem(item)">
            {{ item }}
          </n-tag>
        </n-space>
      </div>

      <!-- 搜索结果区域 -->
      <div v-if="searchResults.length > 0 || searchError" class="search-results">
        <n-card title="搜索结果" size="small">
          <!-- 错误信息 -->
          <n-alert v-if="searchError" type="error" :title="searchError" style="margin-bottom: 16px" />

          <!-- 结果列表 -->
          <n-list v-if="searchResults.length > 0" hoverable clickable>
            <n-list-item v-for="(item, index) in searchResults" :key="index">
              <n-thing>
                <template #header>
                  <n-text strong>{{ item.DocNo || '未知订单号' }}</n-text>
                </template>
                <template #description>
                  <n-space>
                    <n-tag size="small" type="info">ID: {{ item.id || 'N/A' }}</n-tag>
                  </n-space>
                </template>
              </n-thing>
            </n-list-item>
          </n-list>

          <!-- 无结果提示 -->
          <n-empty v-if="searchResults.length === 0 && !searchError && hasSearched"
            description="未找到相关销售订单" size="small" />
        </n-card>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer">
      <div class="footer-links">
        <n-text depth="3" class="copyright-text">
          技术支持 信息部
        </n-text>
      </div>
      <div class="copyright">
        <n-text depth="3" class="copyright-text">
          版权所有 © 广东左向科技有限公司
        </n-text>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Search24Regular as SearchIcon } from '@vicons/fluent'
import { U9Service, type SalesOrder } from '@render/api'

// 搜索查询字符串
const searchQuery = ref('')

// 搜索相关状态
const searchLoading = ref(false)
const searchResults = ref<SalesOrder[]>([])
const searchError = ref('')
const hasSearched = ref(false)

// 防抖定时器
let debounceTimer: NodeJS.Timeout | null = null

// 快捷搜索列表
const hotSearchList = ref([

])

// 防抖处理输入变化
const handleInputChange = () => {
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  // 清空之前的错误信息
  searchError.value = ''

  // 如果输入为空，清空结果
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    hasSearched.value = false
    return
  }

  // 设置新的防抖定时器（500ms延迟）
  debounceTimer = setTimeout(() => {
    performSearch()
  }, 500)
}

// 执行搜索
const performSearch = async () => {
  if (!searchQuery.value.trim()) return

  searchLoading.value = true
  searchError.value = ''
  hasSearched.value = true

  try {
    console.log('搜索销售订单:', searchQuery.value)
    const result = await U9Service.getSalesOrder(searchQuery.value)
    console.log('搜索结果:', result)

    // 处理搜索结果
    if (result && typeof result === 'object') {
      // 如果返回的是单个对象，转换为数组
      if ('DocNo' in result || 'id' in result) {
        searchResults.value = [result]
      } else if (Array.isArray(result)) {
        searchResults.value = result
      } else {
        searchResults.value = []
      }
    } else {
      searchResults.value = []
    }

    console.log('搜索结果:', searchResults.value)
  } catch (error: any) {
    console.error('搜索失败:', error)
    searchError.value = error.message || '搜索失败，请稍后重试'
    searchResults.value = []
  } finally {
    searchLoading.value = false
  }
}

// 处理搜索（回车键或点击搜索按钮）
const handleSearch = () => {
  if (!searchQuery.value.trim()) return

  // 清除防抖定时器，立即执行搜索
  if (debounceTimer) {
    clearTimeout(debounceTimer)
    debounceTimer = null
  }

  performSearch()
}



// 搜索热门项目
const searchHotItem = (item: string) => {
  searchQuery.value = item
  handleSearch()
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  console.log('Logo图片加载失败:', event)
  // 可以设置默认图片或隐藏图片
}
</script>

<style scoped lang="less">
/* 去除a标签的默认下划线 */
a {
  text-decoration: none;
}

/* 去除a标签的默认颜色 */
a {
  color: inherit;
}

/* 去除a标签被点击时的外部轮廓线 */
a:focus {
  outline: none;
}

/* 去除a标签的不同状态下的下划线 */
a:link,
a:visited,
a:hover,
a:active {
  text-decoration: none;
}

.baidu-container {
  min-height: 100vh - 8rem;
  display: flex;
  flex-direction: column;
}

/* 顶部导航 */
.top-nav {
  padding: 8px 20px;
  display: flex;
  justify-content: flex-end;
  border-bottom: 1px solid #70757a;
}

.nav-right {
  display: flex;
  gap: 16px;
}

.nav-link {
  color: #70757a;
  font-size: 13px;
  padding: 4px 8px;
}

.nav-link:hover {
  color: #315efb;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Logo区域 */
.logo-section {
  margin-bottom: 30px;
  text-align: center;
}

.logo-image {
  max-width: 270px;
  height: auto;
  display: block;
  margin: 0 auto;
}

/* 搜索区域 */
.search-section {
  width: 100%;
  max-width: 640px;
  margin-bottom: 30px;
}

.search-wrapper {
  margin-bottom: 30px;
  position: relative;
}

// .search-input {
//   width: 100%;
//   border-radius: 10px !important;
//   transition: border-color 0.2s ease;
// }


// .search-input :deep(.n-input__input-el) {
//   font-size: 16px;
//   border: none;
// }

// .search-input :deep(.n-input__border) {
//   border: none;
// }

// .search-input :deep(.n-input__state-border) {
//   border: none;
// }

.search-icon {
  color: #9aa0a6;
  margin-left: 8px;
}

.search-btn-suffix {
  color: #315efb;
  margin-right: 8px;
}

/* 搜索按钮 */
.button-section {
  display: flex;
  justify-content: center;
}

.search-button {
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  min-width: 108px;
  height: 38px;
  color: #3c4043;
  transition: all 0.2s ease;
}

.primary-btn {
  background: #315efb;
  color: white;
  border-color: #315efb;
}

.primary-btn:hover {
  background: #1e4ba8;
  border-color: #1e4ba8;
}

.secondary-btn:hover {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-color: #dadce0;
}

/* 推荐 */
.hot-search {
  margin-top: 20px;
  max-width: 640px;
  text-align: center;
}

.hot-tag {
  background: #f1f3f4;
  color: #5f6368;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 2px;
}

.hot-tag:hover {
  background: #e8f0fe;
  color: #315efb;
}

/* 搜索结果 */
.search-results {
  width: 100%;
  max-width: 800px;
  margin-top: 30px;
}

.search-results :deep(.n-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-results :deep(.n-list-item) {
  padding: 12px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.search-results :deep(.n-list-item:hover) {
  background: #f8f9fa;
}

.search-results :deep(.n-thing-header) {
  font-size: 16px;
  color: #315efb;
  margin-bottom: 8px;
}

.search-results :deep(.n-tag) {
  font-size: 12px;
}

/* 底部信息 */
.footer {
  margin-top: auto;
  padding: 20px;
  text-align: center;
  border-top: 1px solid #9aa0a6;
}

.footer-links {
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.footer-link {
  color: #70757a;
  font-size: 13px;
  padding: 4px 8px;
}

.footer-link:hover {
  color: #315efb;
}

.copyright-text {
  font-size: 12px;
  color: #70757a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 20px 16px;
  }

  .logo-image {
    max-width: 200px;
  }

  .search-section {
    max-width: 100%;
  }

  .button-section :deep(.n-space) {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .search-button {
    width: 200px;
  }

  .nav-right {
    gap: 8px;
  }

  .footer-links {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .top-nav {
    padding: 8px 12px;
  }

  .nav-right {
    gap: 4px;
  }

  .nav-link {
    font-size: 12px;
    padding: 2px 4px;
  }

  .logo-image {
    max-width: 150px;
  }

  .search-input :deep(.n-input__input-el) {
    font-size: 14px;
  }
}
</style>
