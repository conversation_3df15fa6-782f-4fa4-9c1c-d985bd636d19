/// <reference types="vitest" />
import {join} from 'node:path'
import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import {VitePluginDoubleshot} from 'vite-plugin-doubleshot'
import DefineOptions from 'unplugin-vue-define-options/vite'

export default defineConfig({
    root: join(__dirname, 'src/render'),
    plugins: [
        vue(),
        DefineOptions(),
        VitePluginDoubleshot({
            type: 'electron',
            main: 'dist/main/index.js',
            entry: 'src/main/index.ts',
            outDir: 'dist/main',
            external: ['electron'],
            electron: {
                build: {
                    config: './electron-builder.config.js',
                    cliOptions: {
                        publish: 'never', // "onTag" | "onTagOrDraft" | "always" | "never"
                    },
                },
                preload: {
                    entry: 'src/preload/index.ts',
                    outDir: 'dist/preload',
                },
            },
        }),
    ],
    resolve: {
        alias: {
            '@render': join(__dirname, 'src/render'),
            '@main': join(__dirname, 'src/main'),
            '@common': join(__dirname, 'src/common'),
        },
    },
    base: './',
    build: {
        outDir: join(__dirname, 'dist/render'),
        emptyOutDir: true,
    },
    server: {
        proxy: {
            // 代理U9 API请求
            '/api/u9': {
                target: 'http://192.168.7.237/U9C/webapi',
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api\/u9/, ''),
                configure: (proxy, options) => {
                    proxy.on('error', (err, req, res) => {
                        console.log('proxy error', err);
                    });
                    proxy.on('proxyReq', (proxyReq, req, res) => {
                        console.log('Sending Request to the Target:', req.method, req.url);
                    });
                    proxy.on('proxyRes', (proxyRes, req, res) => {
                        console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
                    });
                }
            }
        }
    },
    test: { // e2e tests
        include: ['./tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
        testTimeout: 30_000,
        hookTimeout: 30_000,
    },
    css: {
        preprocessorOptions: {
            less: {
                javascriptEnabled: true
            },
        },
    },
})
